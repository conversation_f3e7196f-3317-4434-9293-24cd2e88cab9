#!/usr/bin/env tsx

/**
 * RBAC Functionality Demonstration
 * 
 * This script demonstrates that the RBAC system is working correctly
 * by testing real scenarios with different user roles and permissions.
 */

import { db } from './src/lib/db';
import { hasPermission, requirePermission } from './src/lib/rbac/rbac-service';
import { PermissionContext } from './src/lib/rbac/types';
import { logger } from './src/lib/logger';

interface TestScenario {
  name: string;
  description: string;
  test: () => Promise<boolean>;
}

class RbacFunctionalityDemo {
  private testUsers: any[] = [];
  private testOrganizations: any[] = [];

  async setup() {
    console.log('🔧 Setting up RBAC functionality demo...');

    // Create test organizations
    const org1 = await db.organization.findFirst({
      where: { name: 'Demo Org 1' },
    }) || await db.organization.create({
      data: {
        name: 'Demo Org 1',
        status: 'ACTIVE',
      },
    });

    const org2 = await db.organization.findFirst({
      where: { name: 'Demo Org 2' },
    }) || await db.organization.create({
      data: {
        name: 'Demo Org 2',
        status: 'ACTIVE',
      },
    });

    this.testOrganizations = [org1, org2];

    // Create test users
    const adminUser = await db.user.upsert({
      where: { email: '<EMAIL>' },
      update: { organizationId: org1.id, role: 'ADMIN' },
      create: {
        email: '<EMAIL>',
        name: 'Demo Admin',
        role: 'ADMIN',
        organizationId: org1.id,
      },
    });

    const orgAdminUser = await db.user.upsert({
      where: { email: '<EMAIL>' },
      update: { organizationId: org1.id, role: 'ORGANIZATION_ADMIN' },
      create: {
        email: '<EMAIL>',
        name: 'Demo Org Admin',
        role: 'ORGANIZATION_ADMIN',
        organizationId: org1.id,
      },
    });

    const regularUser = await db.user.upsert({
      where: { email: '<EMAIL>' },
      update: { organizationId: org1.id, role: 'ORGANIZATION_USER' },
      create: {
        email: '<EMAIL>',
        name: 'Demo User',
        role: 'ORGANIZATION_USER',
        organizationId: org1.id,
      },
    });

    const otherOrgUser = await db.user.upsert({
      where: { email: '<EMAIL>' },
      update: { organizationId: org2.id, role: 'ORGANIZATION_USER' },
      create: {
        email: '<EMAIL>',
        name: 'Demo Other Org User',
        role: 'ORGANIZATION_USER',
        organizationId: org2.id,
      },
    });

    this.testUsers = [adminUser, orgAdminUser, regularUser, otherOrgUser];
    console.log('✅ Setup completed');
  }

  async runDemo() {
    console.log('\n🚀 Running RBAC Functionality Demo...\n');

    const scenarios: TestScenario[] = [
      {
        name: 'Admin Permissions',
        description: 'Admin users should have broad permissions',
        test: async () => {
          const admin = this.testUsers[0];
          const context: PermissionContext = {
            userId: admin.id,
            organizationId: admin.organizationId,
          };

          const canManageUsers = await hasPermission('assign:role', context);
          const canManageRoles = await hasPermission('create:role', context);
          const canManagePlatform = await hasPermission('manage:platform', context);

          return canManageUsers && canManageRoles && canManagePlatform;
        },
      },

      {
        name: 'Organization Admin Permissions',
        description: 'Org admins should have organization-level permissions but not platform-level',
        test: async () => {
          const orgAdmin = this.testUsers[1];
          const context: PermissionContext = {
            userId: orgAdmin.id,
            organizationId: orgAdmin.organizationId,
          };

          const canManageUsers = await hasPermission('assign:role', context);
          const canManageRoles = await hasPermission('create:role', context);
          const canManagePlatform = await hasPermission('manage:platform', context);

          return canManageUsers && canManageRoles && !canManagePlatform;
        },
      },

      {
        name: 'Regular User Permissions',
        description: 'Regular users should have limited permissions',
        test: async () => {
          const user = this.testUsers[2];
          const context: PermissionContext = {
            userId: user.id,
            organizationId: user.organizationId,
          };

          const canReadUser = await hasPermission('read:user', context);
          const canCreateUser = await hasPermission('assign:role', context);
          const canManageRoles = await hasPermission('create:role', context);

          return canReadUser && !canCreateUser && !canManageRoles;
        },
      },

      {
        name: 'Organization Isolation',
        description: 'Users should not access data from other organizations',
        test: async () => {
          const user1 = this.testUsers[2]; // Org 1 user
          const user2 = this.testUsers[3]; // Org 2 user

          const context1: PermissionContext = {
            userId: user1.id,
            organizationId: user2.organizationId, // Trying to access org 2
          };

          const canAccessOtherOrg = await hasPermission('read:user', context1);
          return !canAccessOtherOrg;
        },
      },

      {
        name: 'Permission Context Validation',
        description: 'Permissions should work with proper context',
        test: async () => {
          const user = this.testUsers[1]; // Org admin
          const context: PermissionContext = {
            userId: user.id,
            organizationId: user.organizationId,
          };

          try {
            await requirePermission('read:user', context);
            return true;
          } catch (error) {
            return false;
          }
        },
      },

      {
        name: 'Carbon Credit Permissions',
        description: 'Users should have appropriate carbon credit permissions',
        test: async () => {
          const orgAdmin = this.testUsers[1];
          const context: PermissionContext = {
            userId: orgAdmin.id,
            organizationId: orgAdmin.organizationId,
          };

          const canCreateCredit = await hasPermission('create:carbon_credit', context);
          const canReadCredit = await hasPermission('read:carbon_credit', context);
          const canUpdateCredit = await hasPermission('update:carbon_credit', context);

          return canCreateCredit && canReadCredit && canUpdateCredit;
        },
      },

      {
        name: 'Wallet Permissions',
        description: 'Users should have appropriate wallet permissions',
        test: async () => {
          const user = this.testUsers[2]; // Regular user
          const context: PermissionContext = {
            userId: user.id,
            organizationId: user.organizationId,
          };

          const canReadWallet = await hasPermission('read:wallet', context);
          const canCreateWallet = await hasPermission('create:wallet', context);
          const canDeleteWallet = await hasPermission('delete:wallet', context);

          return canReadWallet && !canCreateWallet && !canDeleteWallet;
        },
      },

      {
        name: 'Team Permissions',
        description: 'Users should have appropriate team permissions',
        test: async () => {
          const orgAdmin = this.testUsers[1];
          const context: PermissionContext = {
            userId: orgAdmin.id,
            organizationId: orgAdmin.organizationId,
          };

          const canCreateTeam = await hasPermission('create:team', context);
          const canManageTeam = await hasPermission('add:team:member', context);

          return canCreateTeam && canManageTeam;
        },
      },
    ];

    let passed = 0;
    let failed = 0;

    for (const scenario of scenarios) {
      try {
        console.log(`🧪 Testing: ${scenario.name}`);
        console.log(`   ${scenario.description}`);
        
        const result = await scenario.test();
        
        if (result) {
          console.log(`   ✅ PASSED\n`);
          passed++;
        } else {
          console.log(`   ❌ FAILED\n`);
          failed++;
        }
      } catch (error) {
        console.log(`   ❌ ERROR: ${error instanceof Error ? error.message : 'Unknown error'}\n`);
        failed++;
      }
    }

    console.log('📊 Demo Results:');
    console.log(`   ✅ Passed: ${passed}`);
    console.log(`   ❌ Failed: ${failed}`);
    console.log(`   📈 Total: ${passed + failed}`);
    console.log(`   🎯 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

    if (failed === 0) {
      console.log('\n🎉 All RBAC functionality tests passed! The system is working correctly.');
    } else {
      console.log('\n⚠️  Some functionality tests failed. Please review the implementation.');
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up demo data...');
    
    try {
      await db.user.deleteMany({
        where: {
          email: {
            in: this.testUsers.map(u => u.email),
          },
        },
      });

      await db.organization.deleteMany({
        where: {
          id: {
            in: this.testOrganizations.map(o => o.id),
          },
        },
      });

      console.log('✅ Cleanup completed');
    } catch (error) {
      console.log(`⚠️  Cleanup warning: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

async function main() {
  const demo = new RbacFunctionalityDemo();
  
  try {
    await demo.setup();
    await demo.runDemo();
  } catch (error) {
    console.error('❌ Demo failed:', error);
  } finally {
    await demo.cleanup();
    await db.$disconnect();
  }
}

// Run the demo
if (require.main === module) {
  main().catch(console.error);
}

export { RbacFunctionalityDemo };
